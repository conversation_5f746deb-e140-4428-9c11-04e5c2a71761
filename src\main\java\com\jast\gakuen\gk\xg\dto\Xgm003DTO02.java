/*
 * Xgm003DTO02.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import com.jast.gakuen.core.common.BaseDTO;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 納付金配当/割当情報取得戻り値DTO
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003DTO02 extends BaseDTO {

	/**
	 * 年度
	 */
	protected int nendo;

	/**
	 * 納付金コード
	 */
	protected String payCd;

	/**
	 * パターンコード
	 */
	protected String patternCd;

	/**
	 * 分納区分コード
	 */
	protected int bunnoKbnCd;
        
    /**
	 * 分納区分名称
	 */
	protected String bunnoKbnName;

	/**
	 * 分割NO
	 */
	protected int bunkatsuNo;

	/**
	 * 納付金名称
	 */
	protected String payName;

	/**
	 * 納入期限
	 */
	protected Date payLimitDate;

	/**
	 * 出力日
	 */
	protected Date outputDate;

	/**
	 * 延納完了フラグ
	 */
	protected boolean ennoKanryoFlg = false;

	// ========== CSV出力用追加フィールド ==========

	/**
	 * 学籍番号
	 */
	protected String gaksekiCd;

	/**
	 * 管理番号
	 */
	protected Long kanriNo;

	/**
	 * 顧客番号（委託人コード）
	 */
	protected String kokyakuNo;

	/**
	 * 確認番号
	 */
	protected String kakuninNo;

	/**
	 * コンビニネットコード
	 */
	protected String konbiniNetCd;

	/**
	 * 請求金額
	 */
	protected Long seikyuGaku;

	/**
	 * 元金
	 */
	protected Long gankin;

	/**
	 * 延滞金額
	 */
	protected Long entaiGaku;

	/**
	 * 消費税
	 */
	protected Long shohizei;

	/**
	 * 請求内容カナ
	 */
	protected String seikyuNaiyoKana;

	/**
	 * 請求内容漢字
	 */
	protected String seikyuNaiyoKanji;

	/**
	 * 氏名カナ
	 */
	protected String shimeiKana;

	/**
	 * 氏名漢字
	 */
	protected String shimeiKanji;

	/**
	 * 請求情報有効期限
	 */
	protected Date seikyuJohoYukoKigen;
}
