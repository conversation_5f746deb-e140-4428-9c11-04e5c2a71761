/*
 * Xgm003CollectiveOutputDTO01.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.ArrayList;
import java.util.List;

import com.jast.gakuen.core.common.dto.BaseCollectiveOutputDTO;
import com.jast.gakuen.core.common.util.GakuenProperty;
import com.jast.gakuen.gk.xg.async.Xgm003Async;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書（帳票出力）DTO(非同期処理)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003CollectiveOutputDTO01 extends BaseCollectiveOutputDTO {

	/**
	 * コンストラクタ
	 */
	public Xgm003CollectiveOutputDTO01() {
		// プロダクトコード
		setPrdCd(Xgm003Async.PRD_CD);
		// 非同期処理ＩＤ
		setAsyncExecId(Xgm003Async.ASYNC_EXEC_ID);
		// 処理コード
		setExecCd(Xgm003Async.EXEC_PACKAGE_OUTPUT);
		// 非同期処理クラス
		setAsyncExecClass(Xgm003Async.class);
		// チェックのみフラグ
		setCheckOnly(false);
		// 処理続行確認フラグ
		setContinueConfirm(true);
		// 言語コード
		setLangCd(GakuenProperty.getLangCd());
	}

	/**
	 * 検索条件ヘッダ
	 */
	private Xgm003ConditionDTO02 conditionHeader;

	/**
	 * 検索条件
	 */
	private Xgm003ConditionDTO02 condition;

	/**
	 * 納付金リスト
	 */
	private List<Xgm003DTO02> payList = new ArrayList<>();

	/**
	 * 発行対象状況区分（全額未納）
	 */
	private boolean hakkoTgtZengakuMino;

	/**
	 * 発行対象状況区分（一部）
	 */
	private boolean hakkoTgtIchibuMino;

	/**
	 * 学籍番号リスト
	 */
	private List<String> gaksekiCdList = new ArrayList<>();
}
